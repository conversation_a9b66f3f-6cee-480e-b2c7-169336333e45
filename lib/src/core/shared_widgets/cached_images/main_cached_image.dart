import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/icon_widget/icon_widget.dart';
import 'package:shimmer/shimmer.dart';

class BaseCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final double? loadingHeight;
  final double? loadingWidth;
  final BoxFit? fit;
  final double? radius;
  final bool cachedImage;

  const BaseCachedImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.loadingHeight,
    this.loadingWidth,
    this.fit = BoxFit.cover,
    this.radius,
    this.cachedImage = true,
  });

  @override
  Widget build(BuildContext context) {
    final errorWidget = const IconWidget(icon: Assets.iconsHelp)
        .paddingAll(AppSpaces.xlLargePadding);

    if (kIsWeb || !cachedImage) {
      return SizedBox(
        height: height,
        width: width,
        child: Image.network(
          imageUrl,
          height: height,
          width: width,
          fit: fit,
          errorBuilder: (context, error, stackTrace) => errorWidget,
        ),
      );
    }

    return SizedBox(
      height: height,
      width: width,
      child: FastCachedImage(
        fadeInDuration: const Duration(milliseconds: 150),
        url: imageUrl,
        height: height,
        width: width,
        fit: fit,
        loadingBuilder: (context, url) => loadingShimmerWidget(),
        errorBuilder: (context, url, error) => errorWidget,
      ),
    );
  }

  Widget loadingShimmerWidget() => Center(
        child: Shimmer(
          gradient: LinearGradient(
            colors: [
              Colors.grey[300]!,
              Colors.grey[100]!,
            ],
          ),
          child: Container(
              height: loadingHeight ?? height,
              width: loadingWidth ?? width,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: radius == null
                    ? null
                    : BorderRadius.all(Radius.circular(radius!)),
              )),
        ),
      );
}
