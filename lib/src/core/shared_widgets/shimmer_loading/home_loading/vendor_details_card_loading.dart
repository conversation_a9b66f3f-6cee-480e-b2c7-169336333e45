import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

import '../../../../screens/home/<USER>/home_page.dart';
import '../main_shimmer_loading.dart';

class VendorDetailsCardLoading extends StatelessWidget {
  const VendorDetailsCardLoading({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return MainShimmerLoading(
        child: SizedBox(
      height: 240.h,
      child: WaveContainer(
        color: ColorManager.shimmerBaseColor,
        child: SizedBox(),
      ),
    ));
  }
}
