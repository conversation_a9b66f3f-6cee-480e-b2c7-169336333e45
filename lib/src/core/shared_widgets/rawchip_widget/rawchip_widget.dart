import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

class RawChipWidget extends StatelessWidget {
  final String? title;
  final String? label;
  final Function(bool) onSelected;
  final bool isSelected;

  const RawChipWidget(
      {super.key,
      this.title,
      this.label,
      required this.onSelected,
      required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (title != null)
          Text(
            title!,
            style: context.greyLabelLarge,
          ),
        RawChip(
          labelStyle: isSelected ? context.whiteLabelLarge : context.labelLarge,
          label: Text(label!),
          // padding: const EdgeInsets.symmetric(
          //     horizontal: 12, vertical: 15),
          //shape: StadiumBorder(side: BorderSide(color: Theme.of(context).focusColor.withOpacity(0.05))),
          onSelected: onSelected,
        )
      ],
    );
  }
}
