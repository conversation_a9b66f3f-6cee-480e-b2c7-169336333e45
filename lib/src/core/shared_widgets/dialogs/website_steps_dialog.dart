import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/buttons/base_text_button.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view/base_add_category_screen.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/category_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/view/add_product/add_product.dart';
import 'package:idea2app_vendor_app/src/screens/products/view_model/products_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:provider/provider.dart';

import '../../../screens/categories/view/add_and_edit_category/add_category.dart';

class WebSiteStepsDialog extends HookWidget {
  const WebSiteStepsDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final categoryVM = context.read<CategoryVM>();
    final productVM = context.read<ProductVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        categoryVM.getCategories(context);
        productVM.getAllProducts(context);
      });
      return () {};
    }, []);

    return Consumer2<CategoryVM, AuthVM>(
      builder: (context, categoryVM, authVM, child) {
        final vendorData = authVM.currentVendor;

        return StatefulBuilder(builder: (context, setState) {
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                vendorData?.isFree == true
                    ? context.tr.stepsToCreateYourFreeWebSite
                    : context.tr.stepsToCreateYourWebSite,
                style: context.subHeadLine,
                textAlign: TextAlign.center,
              ),
              context.largeGap,
              StepsWidget(
                  isDone: categoryVM.categories.isNotEmpty,
                  title: context.tr.addYourFirstCategory,
                  onTap: () async {
                    await context.to(const BaseAddCategoryScreen());

                    setState(() {});
                  }),
              StepsWidget(
                  isDone: productVM.productsByCategory.isNotEmpty,
                  title: context.tr.addYourFirstProduct,
                  onTap: () async {
                    if (categoryVM.categories.isNotEmpty) {
                      await context.to(AddProductScreen(
                          isFromStepsDialog: true,
                          category: categoryVM.categories.first));
                      setState(() {});
                    } else {
                      context.showBarMessage(context.tr.pleaseAddCategoryFirst,
                          isError: true);
                    }
                  }),
              if (productVM.productsByCategory.isNotEmpty) ...[
                context.mediumGap,
                SizedBox(
                  height: 45,
                  child: Button(
                      label: context.tr.myWebsiteLink,
                      radius: AppRadius.baseRadius,
                      fontSize: 16,
                      onPressed: () {
                        final websiteLink = authVM.isFree
                            ? AppConsts.vendorWebsiteLink()
                            : vendorData?.websiteLink ??
                                AppConsts.vendorWebsiteLink();

                        openURL(websiteLink);
                      }),
                ),
              ]
            ],
          );
        });
      },
    ).paddingSymmetric(vertical: AppSpaces.smallPadding);
  }
}

class StepsWidget extends StatelessWidget {
  final bool isDone;
  final String title;
  final Function() onTap;

  const StepsWidget(
      {super.key,
      required this.title,
      required this.onTap,
      this.isDone = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        color: context.isDark ? ColorManager.darkGrey : ColorManager.grey,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          if (isDone)
            Text(
              title,
              style: context.subTitle.copyWith(
                  fontWeight: FontWeight.w400,
                  decoration: TextDecoration.lineThrough,
                  decorationColor: context.isDark
                      ? ColorManager.secondaryColor
                      : ColorManager.darkGrey.withOpacity(0.5),
                  color: context.isDark
                      ? ColorManager.iconColor
                      : ColorManager.darkGrey.withOpacity(0.5)),
              textAlign: TextAlign.center,
            )
          else
            Text(
              title,
              style: context.subTitle.copyWith(fontWeight: FontWeight.w400),
              textAlign: TextAlign.center,
            ),
          if (isDone)
            const Padding(
              padding: EdgeInsets.all(AppSpaces.smallPadding + 3),
              child: Icon(
                Icons.check,
              ),
            )
          else
            BaseTextButton(title: context.tr.add, onTap: onTap),
        ],
      ),
    ).paddingSymmetric(vertical: AppSpaces.smallPadding);
  }
}
