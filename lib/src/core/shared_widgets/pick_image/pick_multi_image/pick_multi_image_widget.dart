part of shared_widgets;

class PickMultiImageWidget extends StatelessWidget {
  final List<String>? pickedResult;
  final ValueNotifier<List<BaseMediaModel>>? networkImages;
  final Function(String url) onRemoveImage;

  const PickMultiImageWidget({
    super.key,
    this.networkImages,
    this.pickedResult,
    required this.onRemoveImage,
  });

  @override
  Widget build(BuildContext context) {
    final showPickImageButton =
        (pickedResult == null || pickedResult!.isEmpty) &&
            (networkImages == null || networkImages!.value.isEmpty);

    //! Pick Image Button ========================================
    if (showPickImageButton) {
      return Center(
        child: _PickImageButton(
          allowMultiple: true,
          oldFilesLength: networkImages?.value.length ?? 0,
        ).sized(
          height: 90.h,
          width: 110.w,
        ),
      );
    }

    //! View Network Image ========================================
    return _ViewNetworkImagesList(
      onRemoveImage: onRemoveImage,
      networkImages: networkImages,
      pickedResult: pickedResult,
    );
  }
}
