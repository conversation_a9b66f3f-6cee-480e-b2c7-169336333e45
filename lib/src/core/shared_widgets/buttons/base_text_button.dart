import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

class BaseTextButton extends StatelessWidget {
  final String title;
  final Function() onTap;
  final TextStyle? style;
  final bool withUnderline;
  final bool isZeroPadding;

  const BaseTextButton({
    super.key,
    required this.title,
    required this.onTap,
    this.style,
    this.withUnderline = false,
    this.isZeroPadding = false,
  });

  @override
  Widget build(BuildContext context) {
    return TextButton(
        style: TextButton.styleFrom(
          padding: isZeroPadding ? EdgeInsets.zero : null,
          minimumSize: isZeroPadding ? const Size(0, 0) : null,
          tapTargetSize:
              isZeroPadding ? MaterialTapTargetSize.shrinkWrap : null,
        ),
        onPressed: onTap,
        onLongPress: () {
          Clipboard.setData(ClipboardData(text: title));
        },
        child: Text(
          title,
          style: style ??
              context.labelLarge.copyWith(
                  color: ColorManager.primaryColor,
                  decoration: withUnderline ? TextDecoration.underline : null,
                  decorationColor: ColorManager.primaryColor),
        ));
  }
}
