import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';

class RefreshIndicatorWidget extends StatelessWidget {
  final Widget child;
  final Future<void> Function() onRefresh;

  const RefreshIndicatorWidget({
    super.key,
    required this.child,
    required this.onRefresh,
  });

  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      onRefresh: onRefresh,
      color: ColorManager.primaryColor,
      child: child,
    );
  }
}
