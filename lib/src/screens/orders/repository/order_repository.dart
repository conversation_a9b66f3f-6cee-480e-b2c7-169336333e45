import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/core/services/printing_service.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/filter_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/orders_statistics_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/store_order_invoices.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:idea2app_vendor_app/src/screens/shared/app_settings/view_model/app_settings_view_model.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';

import 'invoices_repos/bulk_invoice/bulk_order_invoices.dart';
import 'invoices_repos/online_invoice/main_online_order_invoices.dart';

List<OrderModel> getOrdersData(response) {
  final orderData =
      (response as List).map((order) => OrderModel.fromJson(order)).toList();

  return orderData;
}

class OrderRepo {
  final BaseApiServices _networkApiServices;

  OrderRepo(this._networkApiServices);

  Future<List<OrderModel>> getOrders({
    FilterModel? orderFilter,
  }) async {
    try {
      //? For online payments (Dr Hoopa)
      // const onlyInvoiceIdOrders = '&filters[invoice_id][\$null]';
      // const vendorIsDrHoopaOrVendorIsNull =
      //     '&filters[\$or][0][vendor][business_name][\$eq]=dr-hoopa&filters[\$or][1][vendor][\notNull]';

      final url = '${ApiEndPoints.orders}${orderFilter?.filter ?? ''}';

      final response = await _networkApiServices.getResponse(
        url,
        withOutVendor: false,
      );

      final orderData = compute(getOrdersData, response);

      return orderData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  Future<OrderStatisticsModel> getOrderStatistics() async {
    try {
      final response = await _networkApiServices.getResponse(
        ApiEndPoints.ordersStatistics,
        sortByCreatedAt: false,
      );

      final orderStatisticsData = OrderStatisticsModel.fromJson(response);

      return orderStatisticsData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e, s) {
      Log.e("Order Statistics Error $e\n$s");
      rethrow;
    }
  }

  Future<List<OrderModel>> getHomeOrders({
    FilterModel? orderFilter,
  }) async {
    try {
      final url = '${ApiEndPoints.orders}${orderFilter?.filter ?? ''}';

      final response = await _networkApiServices.getResponse(
        url,
        withOutVendor: false,
      );

      final orderData = compute(getOrdersData, response);

      return orderData;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  Future<List<int>> getAllWebHooks() async {
    try {
      final responseBody = await _networkApiServices.getResponse(
        ApiEndPoints.webHooks,
        withOutVendor: true,
      );
      final responseData = responseBody as List?;

      if (responseData == null) return [];

      Log.w('WEBHOOK_RES $responseBody');

      final webHookData = responseData
          .where((element) => element['invoice_status'] == 'paid')
          .toList();

      final webHookInvoiceIds = webHookData
          .map((webHook) => int.parse(webHook['invoice_id']?.toString() ?? '0'))
          .toList();

      Log.w('WEBHOOK_Invoice_Ids $webHookInvoiceIds');

      Log.w('WEBHOOK_Invoice_IdsLLL ${webHookInvoiceIds.length}');

      return webHookInvoiceIds;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    } catch (e) {
      Log.e(e.toString());
      rethrow;
    }
  }

  // Future<bool> checkIfInvoiceExistInDBWebHook(int? invoiceId) async {
  //   try {
  //     final responseBody = await _networkApiServices
  //         .getResponse(ApiEndPoints.webHooks(invoiceId), withOutVendor: true);
  //     final responseData = responseBody as List?;
  //
  //     Log.w('WEBHOOK_RES $responseBody');
  //
  //     return responseBody != null &&
  //         responseData!.isNotEmpty &&
  //         responseData.firstOrNull['invoice_status'] == 'paid';
  //   } on FetchDataException {
  //     rethrow;
  //   } on TimeoutException {
  //     rethrow;
  //   } on SocketException {
  //     rethrow;
  //   } catch (e) {
  //     Log.e(e.toString());
  //     rethrow;
  //   }
  // }

  //! Make Store Order ================================
  Future<void> makeStoreOrder({required OrderModel orderModel}) async {
    try {
      await _networkApiServices.postResponse(
        ApiEndPoints.orders,
        data: orderModel.toCartJson(),
        connectWithVendorRelation: false,
      );
      Log.f('to Json ${orderModel.toCartJson()}');
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Edit Order ================================
  Future<void> editOrder({required OrderModel orderModel}) async {
    try {
      await _networkApiServices.putResponse(
        ApiEndPoints.orders,
        data: orderModel.toJson(),
        connectWithVendorRelation: true,
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Print Order ================================
  Future<String> generateStoreOrderInvoice(BuildContext context,
      {required OrderModel order}) async {
    try {
      final isEng = context.read<AppSettingsVM>().locale.languageCode == 'en';

      var generatedPDF = await generateStoreOrderInvoicePDF(
        context,
        order: order,
        isEnglish: isEng,
      );

      final pdfTitle = 'Order #${order.orderId} Invoice';

      final generatedPDFReportPath =
          await PrintingService.saveAndOpenPdf(generatedPDF, title: pdfTitle);

      return generatedPDFReportPath;
    } catch (e) {
      rethrow;
    }
  }

  //! Print Online Order ================================
  Future<String> generateOnlineOrderInvoice(BuildContext context,
      {required OrderModel order}) async {
    final isEng = context.read<AppSettingsVM>().locale.languageCode == 'en';

    var generatedPDF;

    generatedPDF = await generateOnlineOrderInvoicePDF(
      context,
      order: order,
      isEnglish: isEng,
    );

    final pdfTitle = 'Order #${order.orderId} Invoice';

    final generatedPDFReportPath =
        await PrintingService.saveAndOpenPdf(generatedPDF, title: pdfTitle);

    return generatedPDFReportPath;
  }

  Future<void> callCustomer({required String? phone}) async {
    try {
      final uri = Uri.parse('tel:$phone');

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri)
            .timeout(const Duration(seconds: ApiStrings.timeOutDuration));
      } else {
        throw AppException('Could not launch $uri');
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  // Fixme Make It Other Countries Codes --------------
  Future<void> openWhatsApp({required String phone}) async {
    try {
      // final formattedNumber =
      //     phone.contains('+20') ? phone : '+20${phone.substring(1)}';
      final formattedNumber =
          phone.startsWith('01') == true ? '+2$phone' : phone;

      Log.f("formatted Num $formattedNumber");

      final url = 'https://wa.me/$formattedNumber';

      openURL(url);
    } catch (e) {
      Log.f("error open whatsapp ${e.toString()}");
    }
  }

  //! Print Bulk Orders ================================
  Future<String> generateBulkOrderInvoices(BuildContext context,
      {required List<OrderModel> allOrders}) async {
    try {
      final isEng = context.read<AppSettingsVM>().locale.languageCode == 'en';

      final orders = <OrderModel>[];
      final seenOrderIds = <dynamic>{};

      for (final order in allOrders) {
        if (order.orderId != null && !seenOrderIds.contains(order.orderId)) {
          seenOrderIds.add(order.orderId);
          orders.add(order);
        }
      }

      final generatedPDF = await generateBulkOrderInvoicesPDF(
        context,
        orders: orders,
        isEnglish: isEng,
      );

      final pdfTitle = isEng
          ? 'Bulk Orders Invoice (${orders.length} orders)'
          : 'فاتورة طلبات متعددة (${orders.length} طلب)';

      final generatedPDFReportPath =
          await PrintingService.saveAndOpenPdf(generatedPDF, title: pdfTitle);

      return generatedPDFReportPath;
    } catch (e) {
      rethrow;
    }
  }
}
