import 'package:colornames/colornames.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view_model/auth_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/repository/invoices_repos/online_invoice/widgets/customer_info_section.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/src/widgets/font.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:provider/provider.dart';

import '../store_order_invoices.dart';

Future<pw.Document> generateOnlineOrderInvoicePDF(BuildContext appContext,
    {required OrderModel order, required bool isEnglish}) async {
  final currentVendor = appContext.read<AuthVM>().currentVendor;

  final arabicFont = Font.ttf(
    await rootBundle.load("assets/fonts/cairo/Cairo-Bold.ttf"),
  );

  // Helper functions to check if colors and sizes exist in the order
  bool hasColors =
      order.products?.any((product) => product.color.isNotEmpty) ?? false;
  bool hasSizes =
      order.products?.any((product) => product.size.isNotEmpty) ?? false;

  final pdf = pw.Document();

  englishTable() {
    // Build header row dynamically based on available data
    List<pw.Widget> headerCells = [
      baseTableText(
        appContext.tr.product,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ];

    if (hasSizes) {
      headerCells.add(baseTableText(
        appContext.tr.size,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasColors) {
      headerCells.add(baseTableText(
        appContext.tr.color,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    headerCells.addAll([
      baseTableText(
        appContext.tr.quantity,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.price,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.total,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ]);

    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: headerCells,
      ),
    ];

    // Add productQuantity details to the table
    for (var productQuantity in order.products!) {
      final productTotal =
          productQuantity.actualPrice * productQuantity.quantity!;

      // Build row cells dynamically based on available data
      List<pw.Widget> rowCells = [
        baseTableText(
          productQuantity.nameByLang(appContext),
          arabicFont: arabicFont,
        ),
      ];

      if (hasSizes) {
        rowCells.add(baseTableText(
          productQuantity.size.isEmpty ? '-' : productQuantity.size,
          arabicFont: arabicFont,
        ));
      }

      if (hasColors) {
        rowCells.add(baseTableText(
          productQuantity.color.isEmpty
              ? '-'
              : ColorNames.guess(Color(productQuantity.color.toInt())),
          arabicFont: arabicFont,
        ));
      }

      rowCells.addAll([
        baseTableText(
          productQuantity.quantity.toString(),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(
            appContext,
            price: productQuantity.actualPrice,
          ),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(appContext, price: productTotal),
          arabicFont: arabicFont,
        ),
      ]);

      children.add(
        pw.TableRow(
          children: rowCells,
        ),
      );
    }

    // Build column widths dynamically
    Map<int, pw.TableColumnWidth> columnWidths = {};
    int columnIndex = 0;

    // Product column
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

    // Size column (if exists)
    if (hasSizes) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    }

    // Color column (if exists)
    if (hasColors) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);
    }

    // Quantity, Price, Total columns
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

    return pw.Table(
      columnWidths: columnWidths,
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  arabicTable() {
    //? reverse the order of the columns for Arabic
    // Build header row dynamically based on available data (reversed for Arabic)
    List<pw.Widget> headerCells = [
      baseTableText(
        appContext.tr.total,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.price,
        arabicFont: arabicFont,
        isBold: true,
      ),
      baseTableText(
        appContext.tr.quantity,
        arabicFont: arabicFont,
        isBold: true,
      ),
    ];

    if (hasColors) {
      headerCells.add(baseTableText(
        appContext.tr.color,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    if (hasSizes) {
      headerCells.add(baseTableText(
        appContext.tr.size,
        arabicFont: arabicFont,
        isBold: true,
      ));
    }

    headerCells.add(baseTableText(
      appContext.tr.product,
      arabicFont: arabicFont,
      isBold: true,
    ));

    var children = <pw.TableRow>[
      pw.TableRow(
        verticalAlignment: pw.TableCellVerticalAlignment.middle,
        decoration: const pw.BoxDecoration(
          color: PdfColors.grey300,
        ),
        children: headerCells,
      ),
    ];

    // Add product details to the table
    for (var product in order.products!) {
      final productTotal = product.actualPrice * product.quantity!;

      // Build row cells dynamically based on available data (reversed for Arabic)
      List<pw.Widget> rowCells = [
        baseTableText(
          toInvoiceCurrency(appContext, price: productTotal),
          arabicFont: arabicFont,
        ),
        baseTableText(
          toInvoiceCurrency(appContext, price: product.actualPrice),
          arabicFont: arabicFont,
        ),
        baseTableText(
          product.quantity.toString(),
          arabicFont: arabicFont,
        ),
      ];

      if (hasColors) {
        rowCells.add(baseTableText(
          product.color.isEmpty
              ? '-'
              : ColorNames.guess(Color(product.color.toInt())),
          arabicFont: arabicFont,
        ));
      }

      if (hasSizes) {
        rowCells.add(baseTableText(
          product.size.isEmpty ? '-' : product.size,
          arabicFont: arabicFont,
        ));
      }

      rowCells.add(baseTableText(
        product?.nameByLang(appContext) ?? '-',
        arabicFont: arabicFont,
      ));

      children.add(
        pw.TableRow(
          children: rowCells,
        ),
      );
    }

    // Build column widths dynamically for Arabic (reversed order)
    Map<int, pw.TableColumnWidth> columnWidths = {};
    int columnIndex = 0;

    // Total, Price, Quantity columns
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);

    // Color column (if exists)
    if (hasColors) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);
    }

    // Size column (if exists)
    if (hasSizes) {
      columnWidths[columnIndex++] = const pw.FlexColumnWidth(1);
    }

    // Product column
    columnWidths[columnIndex++] = const pw.FlexColumnWidth(2);

    return pw.Table(
      columnWidths: columnWidths,
      border: pw.TableBorder.all(),
      children: children,
    );
  }

  final vendorImage = await networkImage(currentVendor?.logo?.url ?? '');

  pdf.addPage(
    pw.MultiPage(
      pageFormat: PdfPageFormat.a4,
      build: (pw.Context context) {
        return <pw.Widget>[
          pw.Header(
              level: 0,
              child: pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 10),
                  child: pw.Align(
                      alignment: isEnglish
                          ? pw.Alignment.centerLeft
                          : pw.Alignment.centerRight,
                      child: pw.Column(
                        crossAxisAlignment: isEnglish
                            ? pw.CrossAxisAlignment.start
                            : pw.CrossAxisAlignment.end,
                        children: [
                          pw.Row(
                              mainAxisAlignment:
                                  pw.MainAxisAlignment.spaceBetween,
                              children: [
                                //! Order Id --------------------------------
                                baseTableText(
                                  '${appContext.tr.order_id}: ${isEnglish ? '#${order.orderId}' : '${order.orderId}#'}',
                                  arabicFont: arabicFont,
                                  fontSize: 16,
                                ), //! Order Id --------------------------------
                                baseTableText(
                                  '${order.createdAt?.formatDateToStringWithTime}',
                                  arabicFont: arabicFont,
                                  fontSize: 16,
                                ),
                              ]),

                          //! Vendor Logo --------------------------------
                          if (currentVendor?.logo?.url != null &&
                              (currentVendor?.logo?.url!.isNotEmpty ?? false))
                            pw.Center(
                              child: pw.Image(vendorImage,
                                  height: 150, fit: pw.BoxFit.contain),
                            )
                          else
                            pw.Center(
                              child: pw.Text(
                                currentVendor?.name ?? '',
                                textScaleFactor: 2,
                                textAlign: pw.TextAlign.center,
                                textDirection: pw.TextDirection.rtl,
                                style: pw.TextStyle(
                                  fontSize: 30,
                                  font: arabicFont,
                                  fontWeight: pw.FontWeight.bold,
                                ),
                              ),
                            ),

                          //! CustomerInfo -------------------------------
                          if (isEnglish)
                            englishCustomerInfo(appContext,
                                arabicFont: arabicFont, order: order)
                          else
                            arabicCustomerInfo(appContext,
                                arabicFont: arabicFont, order: order),
                        ],
                      )))),

          pw.SizedBox(height: 10),

          if (isEnglish) englishTable() else arabicTable(),

          pw.SizedBox(height: 10),

          pw.Divider(
            thickness: .9,
            color: PdfColors.black,
          ),

          pw.SizedBox(height: 10),

          //! Total Order Price & Bottom Section
          if (isEnglish)
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        '${appContext.tr.subtotal}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: (order.total! - order.deliveryCost!)),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        '${appContext.tr.delivery_fee}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: order.deliveryCost),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                    ],
                  ),
                ],
              ),
            )
          else
            pw.Padding(
              padding: const pw.EdgeInsets.only(top: 10),
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.end,
                children: [
                  //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: (order.total! - order.deliveryCost!)),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        '${appContext.tr.subtotal}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ), //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext,
                            price: order.deliveryCost),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        '${appContext.tr.delivery_fee}:',
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ), //! Total Price
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      baseTableText(
                        toInvoiceCurrency(appContext, price: order.total),
                        arabicFont: arabicFont,
                        fontSize: 18,
                        isBold: true,
                      ),
                      baseTableText(
                        appContext.tr.totalPrice,
                        arabicFont: arabicFont,
                        isBold: true,
                        fontSize: 18,
                      ),
                    ],
                  ),
                ],
              ),
            ),
        ];
      },
    ),
  );

  return pdf;
}
