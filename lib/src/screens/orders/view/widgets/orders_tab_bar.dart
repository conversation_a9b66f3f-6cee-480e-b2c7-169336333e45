import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/tab_bar_widgets/tab_bar_widget.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/home_tab_bar_provider.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

class OrdersTabBar extends StatelessWidget {
  const OrdersTabBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer2<HomeTabBarVM, OrderVM>(
      builder: (context, homeTabBarVM, orderVM, child) {
        return Padding(
          padding: EdgeInsets.only(
            left: context.isEng ? AppSpaces.smallPadding : 0,
            right: context.isEng ? 0 : AppSpaces.smallPadding,
          ),
          child: TapBarWidget(
            tabs: [context.tr.all, ...OrderStatus.values.map((e) => e.name)],
            initialIndex: homeTabBarVM.currentIndex,
            onTab: (index) {
              final isCurrentIndex = homeTabBarVM.currentIndex == index;

              if (isCurrentIndex) {
                return;
              }

              homeTabBarVM.setCurrentIndex(index);

              final isAll = index == 0;

              orderVM.setOrderFilter(context,
                  filter: orderVM.orderFilter.copyWith(
                    limit: 10,
                    isAll: isAll,
                    orderStatus: isAll ? null : OrderStatus.values[index - 1],
                  ));
            },
          ),
        );
      },
    );
  }
}
