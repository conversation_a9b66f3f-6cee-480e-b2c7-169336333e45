import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model_helper.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/order_details_screen/widgets/order_bottom_section.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/order_details_screen/widgets/order_customer_details/user_order_details.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view/order_details_screen/widgets/order_product_details/order_product_details.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';

import 'widgets/order_top_card_section.dart';

class MainOrderDetailsScreen extends HookWidget {
  final OrderModel order;

  const MainOrderDetailsScreen({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    final orderStatus = useState(order.status);
    final deliveryCost = useState(order.deliveryCost);

    return Consumer<OrderVM>(
      builder: (context, orderVM, child) {
        return Scaffold(
          bottomNavigationBar: OrderBottomSection(
            order: order,
            orderStatus: orderStatus,
            deliveryCost: deliveryCost,
          ),
          appBar: MainAppBar(
            title: context.tr.orderDetails,
            haveBackButton: true,
            actionWidget: Container(
              width: 80.w,
              height: 30.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                  color: orderStatus.value.displayColor),
              alignment: AlignmentDirectional.center,
              child: Text(
                orderStatus.value
                    .getTranslation(context, isConfirmedText: true),
                maxLines: 1,
                overflow: TextOverflow.fade,
                softWrap: false,
                style: context.whiteLabelMedium
                    .copyWith(fontWeight: FontWeight.w600),
              ),
            ),
            isCenterTitle: false,
          ),
          body: ListView(
            padding: EdgeInsets.all(10.h),
            children: [
              //! Order Card
              OrderTopCardSection(order: order),

              context.mediumGap,

              Text(context.tr.customerInfo, style: context.labelLarge),

              context.mediumGap,
              UserOrderDetails(
                order: order,
              ),
              context.smallGap,
              Divider(),
              context.smallGap,
              Text(context.tr.products, style: context.labelLarge),

              context.mediumGap,

              OrderProductDetails(
                order: order,
              ),
            ],
          ),
        );
      },
    );
  }
}
