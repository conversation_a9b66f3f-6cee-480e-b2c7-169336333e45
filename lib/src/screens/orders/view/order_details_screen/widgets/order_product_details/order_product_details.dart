import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/order/order_model.dart';

import 'order_products_list.dart';

class OrderProductDetails extends StatelessWidget {
  final OrderModel order;

  const OrderProductDetails({super.key, required this.order});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
        physics: const NeverScrollableScrollPhysics(),
        itemCount: order.products!.length,
        shrinkWrap: true,
        padding: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
        separatorBuilder: (context, index) => context.mediumGap,
        itemBuilder: (context, index) =>
            OrderProductDetailsWidget(productQuantity: order.products![index]));
  }
}
