import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/screens/cart/view_model/cart_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';
import 'package:idea2app_vendor_app/src/screens/orders/models/products_quantity_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/models/products_model.dart';
import 'package:idea2app_vendor_app/src/screens/store/view/widgets/add_to_cart/product_details_and_quantity_buttons.dart';
import 'package:provider/provider.dart';

import '../../../../../core/consts/api_strings.dart';
import '../../../../../core/shared_widgets/shared_widgets.dart';
import '../../../../products/view/add_product/widgets/fields/colors/product_colors_list.dart';
import '../../../../products/view/add_product/widgets/fields/sizes/product_sizes_list.dart';

class AddToCartWidgetDialog extends HookWidget {
  final ProductModel productModel;
  final Map<String, ValueNotifier> valueNotifiers;

  const AddToCartWidgetDialog(
      {super.key, required this.productModel, required this.valueNotifiers});

  @override
  Widget build(BuildContext context) {
    final quantity = useState(1);
    final isSale = productModel.isSale;

    final selectedSizePrice = useState<ExtraSettingsModel?>(
        (valueNotifiers[ApiStrings.sizes]?.value as List<ExtraSettingsModel>?)
            ?.firstOrNull);

    final totalPrice =
        selectedSizePrice.value?.price ?? productModel.totalPrice ?? 0;
    final totalSalePrice = productModel.salePrice!;

    final finalPrice = (totalPrice * quantity.value);
    final finalSalePrice = (totalSalePrice * quantity.value);

    return Consumer<CartVM>(
      builder: (context, cartVM, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // * Product Details and Quantity ========================
            ProductsDetailsAndQuantityButtons(
              productModel: productModel,
              quantity: quantity,
              totalPrice: finalPrice,
              salePrice: finalSalePrice,
            ),

            // * Colors ========================
            if (productModel.colors.isNotEmpty) ...[
              context.mediumGap,
              ProductColorsList(
                isSingle: true,
                // productColors: productModel.colors,
                selectedColors: valueNotifiers[ApiStrings.colors]
                    as ValueNotifier<List<ExtraSettingsModel>>,
                canAddStock: false,
              ),
            ],

            // * Sizes ========================
            if (productModel.sizes.isNotEmpty) ...[
              context.mediumGap,
              ProductSizesList(
                category: productModel.category!,
                productSize: productModel.sizes,
                isSingle: true,
                valueNotifiers: valueNotifiers,
                canAddStock: false,
                onSelected: (selectedSize) {
                  selectedSizePrice.value = selectedSize;
                },
              ),
            ],

            context.largeGap,

            // * Add Product Button ========================
            Button(
                isLoading: false,
                label: context.tr.addToCart,
                onPressed: () async {
                  await cartVM.saveProductToCart(context,
                      productQuantity: ProductQuantityModel(
                          quantity: quantity.value,
                          totalPrice: isSale ? finalSalePrice : finalPrice,
                          price: totalPrice,
                          product: productModel,
                          color: (valueNotifiers[ApiStrings.colors]?.value
                                      as List<ExtraSettingsModel>?)
                                  ?.firstOrNull
                                  ?.englishName ??
                              '',
                          size: (valueNotifiers[ApiStrings.sizes]?.value
                                      as List<ExtraSettingsModel>?)
                                  ?.firstOrNull
                                  ?.englishName ??
                              ''));
                })
          ],
        );
      },
    );
  }
}
