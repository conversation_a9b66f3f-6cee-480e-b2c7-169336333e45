import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/generated/assets.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/add_vendor/widgets/pricing_drop_down.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/fields/date_picker.dart';
import 'package:idea2app_vendor_app/src/core/widgets/vendors_drop_down.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/pricing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/model/subscription_request_model.dart';
import 'package:idea2app_vendor_app/src/screens/subscription_request/view_model/subscription_request_view_model.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';

class AddNewSubscriptionDialog extends HookWidget {
  const AddNewSubscriptionDialog({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final selectedIndex = useState(0);
    final controllers = {
      ApiStrings.paidAmount: useTextEditingController(),
    };

    final valueNotifiers = {
      ApiStrings.vendor: useState<VendorModel?>(null),
      ApiStrings.vendorType: useState<VendorType>(VendorType.monthly),
      ApiStrings.startDate: useState<DateTime>(DateTime.now()),
      ApiStrings.expireDate:
          useState<DateTime>(DateTime.now().add(const Duration(days: 31))),
      ApiStrings.pricing: useState<PricingModel?>(null),
    };

    final vendorData = valueNotifiers[ApiStrings.vendor]?.value as VendorModel?;
    final pricing = valueNotifiers[ApiStrings.pricing]?.value as PricingModel?;
    final paidAmount = controllers[ApiStrings.paidAmount]!.text;

    final remainingAmount = useState<num>(0);

    return Consumer2<SubscriptionRequestVM, MediaVM>(
      builder: (context, subscriptionRequestVM, mediaVM, child) {
        final isInstapay = selectedIndex.value == 0;

        return BaseDialog(
          isExpanded: true,
          withCloseButton: true,
          isLoading: false,
          backgroundColor: context.appTheme.cardColor,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Center(
                  child: SinglePickImageWidget(
                    title: context.tr.paymentAttachment,
                    pickedResult: mediaVM.filesPaths.firstOrNull,
                  ),
                ),

                context.largeGap,
                Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          selectedIndex.value = 0;
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: isInstapay
                                  ? Border.all(color: Colors.purple, width: 2)
                                  : null),
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.asset(
                                Assets.instapayCash,
                                fit: BoxFit.cover,
                                height: 50,
                              )),
                        ),
                      ),
                    ),
                    context.smallGap,
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          selectedIndex.value = 1;
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: AppSpaces.smallPadding,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              border: selectedIndex.value == 1
                                  ? Border.all(color: Colors.red, width: 2)
                                  : null),
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(10),
                              child: Image.asset(
                                Assets.vCash,
                                fit: BoxFit.cover,
                                height: 50,
                              )),
                        ),
                      ),
                    ),
                  ],
                ),

                context.largeGap,

                // Vendor Drop Down
                VendorDropDownWidget(
                  selectedVendor: valueNotifiers[ApiStrings.vendor]!
                      as ValueNotifier<VendorModel?>,
                  onSelected: (vendor) {
                    valueNotifiers[ApiStrings.vendorType]!.value =
                        vendor!.vendorType ?? VendorType.monthly;
                    valueNotifiers[ApiStrings.pricing]!.value =
                        vendor.pricing ??
                            PricingModel(
                              name: 'Free',
                              price: 0,
                            );
                    valueNotifiers[ApiStrings.startDate]!.value =
                        DateTime.now();

                    final addedDays = vendor.vendorType == VendorType.monthly
                        ? 31
                        : vendor.vendorType == VendorType.quarter
                            ? 92
                            : vendor.vendorType == VendorType.semester
                                ? 184
                                : 365;

                    valueNotifiers[ApiStrings.expireDate]!.value =
                        DateTime.now().add(Duration(days: addedDays));
                    controllers[ApiStrings.paidAmount]!.text =
                        vendor.pricing?.price.toString() ?? '';
                    remainingAmount.value = 0;
                  },
                ),

                context.largeGap,

                // Start Date Picker
                Row(
                  children: [
                    Expanded(
                      child: BaseDatePicker(
                        title: context.tr.startDate,
                        selectedDateNotifier:
                            valueNotifiers[ApiStrings.startDate]!
                                as ValueNotifier<DateTime?>,
                        label: context.tr.startDate,
                      ),
                    ),

                    context.largeGap,

                    // End Date Picker
                    Expanded(
                      child: BaseDatePicker(
                        title: context.tr.expireDate,
                        selectedDateNotifier:
                            valueNotifiers[ApiStrings.expireDate]!
                                as ValueNotifier<DateTime?>,
                        label: context.tr.expireDate,
                      ),
                    ),
                  ],
                ),

                context.largeGap,

                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: kIsWeb ? 0.0 : 12,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                        AppRadius.extraLargeContainerRadius),
                    color: context.isDark
                        ? Colors.grey.withOpacity(.1)
                        : Colors.grey.withOpacity(0.15),
                  ),
                  child: BaseDropDown<VendorType>(
                    isExpanded: true,
                    value: valueNotifiers[ApiStrings.vendorType]!.value,
                    data: VendorType.values
                        .map((e) => DropdownMenuItem(
                              value: e,
                              child: Text(e.toString().split('.').last),
                            ))
                        .toList(),
                    onChanged: (value) {
                      valueNotifiers[ApiStrings.vendorType]!.value = value!;
                      final vendorExpireDate = DateTime.now();
                      final expireDate = valueNotifiers[ApiStrings.expireDate]!;

                      if (value == VendorType.monthly) {
                        expireDate.value =
                            vendorExpireDate.add(const Duration(days: 31));
                      } else if (value == VendorType.quarter) {
                        expireDate.value =
                            vendorExpireDate.add(const Duration(days: 92));
                      } else if (value == VendorType.semester) {
                        expireDate.value =
                            vendorExpireDate.add(const Duration(days: 184));
                      } else if (value == VendorType.annually) {
                        expireDate.value =
                            vendorExpireDate.add(const Duration(days: 365));
                      } else {
                        expireDate.value = null;
                      }
                    },
                  ),
                ),

                context.largeGap,

                // Pricing Plan Drop Down
                PricingDropDown(
                  selectedPricing: valueNotifiers[ApiStrings.pricing]!
                      as ValueNotifier<PricingModel?>,
                  vendorType: valueNotifiers[ApiStrings.vendorType]?.value
                      as VendorType?,
                  paidAmountController: controllers[ApiStrings.paidAmount],
                ),

                context.largeGap,

                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Expanded(
                      child: BaseTextField(
                        title: context.tr.paidAmount,
                        hint: context.tr.paidAmount,
                        isRequired: false,
                        controller: controllers[ApiStrings.paidAmount],
                        onChanged: (value) {
                          final paidAmount = num.tryParse(value) ?? 0;

                          final remainingAmountData =
                              (pricing?.price ?? 0) - paidAmount;

                          remainingAmount.value = remainingAmountData;
                        },
                      ),
                    ),
                    context.smallGap,
                    Padding(
                      padding: const EdgeInsets.only(
                          bottom: AppSpaces.xSmallPadding),
                      child: CircleAvatar(
                        backgroundColor: ColorManager.primaryColor,
                        child: IconButton(
                          onPressed: () {
                            final halfPrice = (pricing?.price ?? 0) / 2;

                            remainingAmount.value = halfPrice.toInt();

                            controllers[ApiStrings.paidAmount]!.text =
                                halfPrice.round().toString();
                          },
                          icon: const Icon(
                            CupertinoIcons.money_pound,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),

                context.mediumGap,

                Text(
                    '${context.tr.remainingAmount} ${remainingAmount.value.toStringAsFixed(0).toCurrency(context)}'),

                context.mediumGap,

                // Send Request Button
                Button(
                  isLoading: subscriptionRequestVM.isLoading,
                  isOutLine: subscriptionRequestVM.isLoading,
                  label: context.tr.sendRequest,
                  onPressed: () async {
                    if (vendorData == null) {
                      context.showBarMessage(context.tr.selectVendor,
                          isError: true);
                      return;
                    }
                    context.back();

                    final isApproved = pricing?.price == num.parse(paidAmount);

                    final subscriptionRequest = SubscriptionRequestModel(
                      vendor: vendorData,
                      isApproved: isApproved,
                      paymentMethod: isInstapay ? 'instapay' : 'vodafone_cash',
                      pricingModel: valueNotifiers[ApiStrings.pricing]?.value
                          as PricingModel?,
                      paidAmount: num.parse(paidAmount),
                    );

                    await subscriptionRequestVM.makeAdminSubscriptionRequest(
                      context,
                      fileResult: mediaVM.filesPaths.firstOrNull ?? '',
                      subscriptionRequest: subscriptionRequest,
                      vendorType: valueNotifiers[ApiStrings.vendorType]?.value
                          as VendorType,
                    );

                    subscriptionRequestVM.getSubscriptionRequests(context);

                    context.read<MediaVM>().clearFiles();
                  },
                  isWhiteText: true,
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
