import 'package:flutter/foundation.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/network/base_api_service.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/response/api_end_points.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/vendor_helper_model.dart';

import '../model/notification_model.dart';

class NotificationRepo {
  final BaseApiServices _networkApiServices;

  NotificationRepo(this._networkApiServices);

  Future<List<NotificationModel>> getNotifications() async {
    // const vendorIsDrHoopaOrVendorIsNull =
    //     '&filters[\$or][0][vendor][business_name][\$eq]=dr-hoopa&filters[\$or][1][vendor][\notNull]';

    final currentVendor = VendorModelHelper.currentVendorBusinessName();

    final filteredNotificationsIfVendorIsNullOrVendorIsSameLikeCurrent =
        '?filters[\$or][0][vendor][business_name][\$eq]=$currentVendor&filters[\$or][1][vendor][\$notNull]';

    final response = await _networkApiServices.getResponse(
      ApiEndPoints.notifications +
          filteredNotificationsIfVendorIsNullOrVendorIsSameLikeCurrent,
      withOutVendor: true,
    );

    final notificationData =
        await compute(responseFromNotificationModel, response);

    return notificationData;
  }
}
