import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/dialogs/base_dialog.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../../shared/media/view_models/media_view_model.dart';
import '../../model/banner_model.dart';
import '../../view_model/banner_VM.dart';

class AddBannerDialog extends HookWidget {
  final BannerModel? banner;

  const AddBannerDialog({super.key, this.banner});

  @override
  Widget build(BuildContext context) {
    final isEdit = banner != null;
    final titleController = useTextEditingController(text: banner?.title);

    return Consumer2<BannerVM, MediaVM>(
      builder: (context, bannerVM, mediaVM, child) {
        Future<void> addAndEditBanner() async {
          if (isEdit) {
            await bannerVM.editBanner(context,
                id: banner?.documentId,
                title: titleController.text,
                fileResult: mediaVM.filesPaths.firstOrNull ?? '');
          } else {
            await bannerVM.addBanner(context,
                title: titleController.text,
                fileResult: mediaVM.filesPaths.firstOrNull ?? '');
          }

          context.read<MediaVM>().clearFiles();
        }

        return BaseDialog(
            withCloseButton: true,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // * Header ===========================
                  Text(
                    isEdit ? context.tr.editBanner : context.tr.addBanner,
                    style: context.subHeadLine,
                  ),
                  context.largeGap,
                  SinglePickImageWidget(
                    width: double.infinity,
                    networkImage: banner?.media?.url,
                    pickedResult: mediaVM.filesPaths.firstOrNull,
                  ),

                  context.largeGap,

                  // * Title ===========================
                  BaseTextField(
                    title: '${context.tr.title} (${context.tr.optional})',
                    controller: titleController,
                    hint: context.tr.title,
                  ),

                  context.largeGap,

                  // * Add Product Button ========================
                  Button(
                    isLoading: bannerVM.isLoading,
                    isOutLine: bannerVM.isLoading,
                    label: isEdit ? context.tr.edit : context.tr.add,
                    onPressed: () async {
                      if (mediaVM.filesPaths.isEmpty &&
                          banner?.media?.url == null) {
                        return context.showBarMessage(
                            context.tr.pleasePickImage,
                            isError: true);
                      } else {
                        return await addAndEditBanner();
                      }
                    },
                    isWhiteText: true,
                  )
                ],
              ),
            ));
      },
    );
  }
}
