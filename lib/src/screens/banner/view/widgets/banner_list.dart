import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/screens/banner/view_model/banner_VM.dart';
import 'package:provider/provider.dart';

import '../../../../core/resources/app_spaces.dart';
import '../../../../core/shared_widgets/loading/loading_widget.dart';
import 'banner_card_widget.dart';

class BannerList extends HookWidget {
  const BannerList({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<BannerVM>(
      builder: (context, bannerVM, child) {
        final banners = bannerVM.bannerList;

        if (bannerVM.isLoading || banners.isEmpty) {
          return EmptyDataWidget(
            message: context.tr.noBanners,
            isLoading: bannerVM.isLoading,
          );
        }


        return ListView.separated(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            itemBuilder: (context, index) {
              return BannerCardWidget(banner: banners[index]);
            },
            separatorBuilder: (context, index) => context.mediumGap,
            itemCount: banners.length);
      },
    );
  }
}
