import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/animated/empty_data_widget.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/utils/logger.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/model/city_cost_model.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/area_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/save_shipping_floating_button.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view/widgets/shipping_card_widget.dart';
import 'package:idea2app_vendor_app/src/screens/shipping/view_model/shipping_view_model.dart';
import 'package:provider/provider.dart';

import 'add_areas_screen.dart';

class AreasScreen extends HookWidget {
  final String cityName;
  final String cityDocumentIdId;

  const AreasScreen(
      {super.key, required this.cityName, required this.cityDocumentIdId});

  @override
  Widget build(BuildContext context) {
    final shippingVM = context.read<ShippingVM>();
    final searchController = useTextEditingController();
    final filteredAreas = useState<List<AreaModel>>([]);

    final cityCost = shippingVM.vendorShipping?.citiesCost?.firstWhereOrNull(
      (city) => city.city?.documentId == cityDocumentIdId,
    );

    final vendorAreas = useState(cityCost?.areas ?? []);

    final shouldRebuild = useState(false);

    return Scaffold(
      floatingActionButton: FloatingActionButton(
          backgroundColor: ColorManager.primaryColor,
          onPressed: () async {
            await context.to(AddAreasScreen(
              cityName: cityName,
              vendorAreas: vendorAreas.value,
              cityDocumentIdId: cityDocumentIdId,
            ));

            // Refresh areas from shippingVM
            final updatedCityCost =
                shippingVM.vendorShipping?.citiesCost?.firstWhereOrNull(
              (city) => city.city?.documentId == cityDocumentIdId,
            );

            vendorAreas.value = updatedCityCost?.areas ?? [];

            shouldRebuild.value = !shouldRebuild.value;
          },
          child: Icon(Icons.add, color: ColorManager.white)),
      appBar: MainAppBar(
        title: context.tr
            .areasIn(context.isEng ? cityName : cityLangToAr(cityName)),
        haveBackButton: true,
        isCenterTitle: false,
        actionWidget: SaveShippingFloatingButton(
          onSave: () {
            for (var area in vendorAreas.value) {
              Log.f(
                  'Area: ${area.nameEn} / ${area.nameAr}, Cost: ${area.cost}, Free Shipping: ${area.isFreeShipping}, Active: ${area.isActive}');
            }

            shippingVM.updateCityAreas(
                cityId: cityDocumentIdId, areas: vendorAreas.value);
          },
        ),
      ),
      body: vendorAreas.value.isEmpty
          ? EmptyDataWidget(message: context.tr.noAreasFound)
          : Column(
              children: [
                Padding(
                  padding: EdgeInsets.all(AppSpaces.mediumPadding),
                  child: BaseTextField(
                    hint: context.tr.search,
                    suffixIcon: searchController.text.isNotEmpty
                        ? Icon(Icons.close,
                                color: context.appTheme.primaryColorDark)
                            .onTap(() {
                            searchController.clear();

                            filteredAreas.value = vendorAreas.value;
                          })
                        : null,
                    withoutEnter: true,
                    controller: searchController,
                    onChanged: (value) {
                      filteredAreas.value = vendorAreas.value
                          .where((area) =>
                              (area.nameEn
                                      ?.toLowerCase()
                                      .contains(value.toLowerCase()) ??
                                  false) ||
                              (area.nameAr
                                      ?.toLowerCase()
                                      .contains(value.toLowerCase()) ??
                                  false))
                          .toList();
                    },
                    icon: Icon(Icons.search,
                        color: context.appTheme.primaryColorDark),
                  ),
                ),
                if (searchController.text.isNotEmpty &&
                    filteredAreas.value.isEmpty)
                  EmptyDataWidget(message: context.tr.noAreasFound)
                else
                  Expanded(
                    child: ListView.separated(
                        padding: EdgeInsets.only(
                            top: AppSpaces.mediumPadding,
                            right: AppSpaces.mediumPadding,
                            left: AppSpaces.mediumPadding,
                            bottom: 80.h),
                        itemBuilder: (context, index) {
                          final area = searchController.text.isNotEmpty
                              ? filteredAreas.value[index]
                              : vendorAreas.value[index];
                          return AreaCardWidget(
                            areas: vendorAreas,
                            area: area,
                            onUpdate: (updatedArea) {
                              vendorAreas.value[index] =
                                  updatedArea; // Update the list
                              vendorAreas.value = List.from(
                                  vendorAreas.value); // Trigger UI update
                            },
                          );
                        },
                        separatorBuilder: (context, index) => context.mediumGap,
                        itemCount: searchController.text.isNotEmpty
                            ? filteredAreas.value.length
                            : vendorAreas.value.length),
                  ),
              ],
            ),
    );
  }
}
