import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/screens/users/view/widgets/user_order_card_widget.dart';
import 'package:provider/provider.dart';

import '../../../core/shared_widgets/animated/empty_data_widget.dart';
import '../../../core/shared_widgets/loading/loading_widget.dart';
import '../view_model/user_view_model.dart';
import 'widgets/send_user_notification_dialog.dart';

class UsersOrdersScreen extends HookWidget {
  const UsersOrdersScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final userVM = context.read<UserVM>();
    // * Search Field ========================
    final queryController = useTextEditingController();
    final isSearch = useState(false);
    final query = useState('');

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        userVM.getUserOrders(context);
      });

      return () {};
    }, []);

    return Scaffold(
      appBar: MainAppBar(
        haveBackButton: true,
        title: context.tr.usersOrders,
      ),
      body: Consumer<UserVM>(
        builder: (context, userOrderVM, child) {
          final users = userOrderVM.searchUsersOrders(context, query.value);

          if (userOrderVM.isLoading) {
            return const Center(child: LoadingWidget());
          }

          if (userOrderVM.userOrders.isEmpty) {
            return Center(
              child: EmptyDataWidget(
                message: context.tr.noUsersFound,
              ),
            );
          }

          return Column(
            children: [
              Button(
                onPressed: () => showDialog(
                  context: context,
                  builder: (context) {
                    return const SendUserNotificationDialog(
                      isTopic: true,
                    );
                  },
                ),
                label: context.tr.sendNotification,
              ).paddingAll(16),
              BaseTextField(
                controller: queryController,
                onChanged: (value) {
                  query.value = value;

                  isSearch.value = value.isNotEmpty;

                  userOrderVM.notify();
                },
                suffixIcon: Icon(
                  isSearch.value ? Icons.close : CupertinoIcons.search,
                ).onTap(() {
                  query.value = '';

                  queryController.clear();

                  isSearch.value = false;

                  userOrderVM.notify();
                }),
                hint: context.tr.search,
                withoutEnter: true,
              ).paddingSymmetric(horizontal: AppSpaces.smallPadding),
              context.mediumGap,
              if (users.isEmpty && isSearch.value)
                Center(
                  child: EmptyDataWidget(
                    message: context.tr.noUsersFound,
                  ),
                ),
              Expanded(
                child: ListView.separated(
                    padding: const EdgeInsets.all(AppSpaces.smallPadding),
                    itemBuilder: (context, index) {
                      final userOrder = users[index];

                      return UserOrderCardWidget(
                        userOrder: userOrder,
                      );
                    },
                    separatorBuilder: (context, index) => context.mediumGap,
                    itemCount: users.length),
              ),
            ],
          );
        },
      ),
    );
  }
}
