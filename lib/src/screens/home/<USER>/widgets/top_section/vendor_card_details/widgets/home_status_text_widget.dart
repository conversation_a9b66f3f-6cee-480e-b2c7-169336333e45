import 'package:flutter/cupertino.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/screens/home/<USER>/widgets/top_section/vendor_card_details/widgets/vendor_card_status_widgets/active_vendor_card/vendor_website_widget.dart';
import 'package:idea2app_vendor_app/src/screens/orders/view_model/order_view_model.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart' hide AppRadius, AppSpaces;

import '../../../../../../../core/resources/theme/theme.dart';
import '../../../../../../auth/models/helper_models/vendor_helper_model.dart';
import '../../../../../../auth/models/vendor_model.dart';
import '../../../../../../auth/view_model/auth_view_model.dart';

class HomeStatusTextWidget extends HookWidget {
  final VendorModel? vendorData;

  const HomeStatusTextWidget(this.vendorData, {super.key});

  @override
  Widget build(BuildContext context) {
    final authVM = context.watch<AuthVM>();

    final isFree = authVM.currentVendor?.vendorType == VendorType.free;

    final isActive =
        isFree || (vendorData?.expireDate?.isAfter(DateTime.now()) ?? true);

    final showPricing = authVM.currentVendor?.showPricing ?? true;

    final isExpired = vendorData?.expireDate?.isBefore(DateTime.now()) ?? true;
    final remainingDays =
        vendorData?.expireDate?.difference(DateTime.now()).inDays.abs();

    final activeText = Text(
      context.isEng
          ? '${context.tr.active} ${remainingDays ?? 0} ${context.tr.days} ${context.tr.left}'
          : '${context.tr.active} ${context.tr.left} ${remainingDays ?? 0} ${context.tr.days}',
      style: context.body.copyWith(
        color: ColorManager.white,
        fontWeight: FontWeight.w400,
        fontSize: 14,
      ),
    );

    final expiredText = Text(
      context.isEng
          ? '${context.tr.expired} ${remainingDays ?? 0} ${context.tr.days} ${context.tr.ago}'
          : '${context.tr.expired} ${context.tr.ago} ${remainingDays ?? 0} ${context.tr.days}',
      style: context.body.copyWith(
        color: ColorManager.white,
        fontWeight: FontWeight.w400,
        fontSize: 14,
      ),
    );
    final pricingText = vendorData?.pricing == null
        ? VendorModelHelper.vendorTypePlan(vendorData?.vendorType, context)
        : context.isEng
            ? (vendorData?.pricing?.name ?? '')
            : (vendorData?.pricing?.nameAr ?? '');

    if (isFree) {
      return Text(
        context.tr.freePlan,
        style: context.whiteTitle.copyWith(
          fontWeight: FontWeight.w500,
        ),
      );
    }
    if (isActive) {
      return Column(
        children: [
          activeText,
          Text(
            pricingText,
            style: context.whiteSubTitle.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    if (isExpired) {
      return Column(
        children: [
          expiredText,
          Text(
            pricingText,
            style: context.whiteSubTitle.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      );
    }

    return const SizedBox.shrink();
  }
}
