import 'dart:async';

import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/shared_view_models/base_view_model.dart';

import '../repository/local_settings_repo.dart';

class AppSettingsVM extends BaseVM {
  final SettingsLocalRepo settingsLocalRepo;

  AppSettingsVM({required this.settingsLocalRepo});

  //! Load Settings ===================================
  Future<void> loadSettings() async {
    _themeMode = settingsLocalRepo.themeMode();
    _locale = settingsLocalRepo.currentLocal();
    notifyListeners();
  }

  Locale _locale = const Locale('en', 'US');

  Locale get locale => _locale;

  //! Update Language  ===================================

  Future<void> updateLanguage(Locale newLocale) async {
    if (_locale == newLocale) return;
    _locale = newLocale;
    await settingsLocalRepo.updateLanguage(newLocale);
    notifyListeners();
  }

  ThemeMode _themeMode = ThemeMode.system;

  ThemeMode get themeMode => _themeMode;

  //! Update Theme  ===================================

  void changeAppMode(ThemeMode? newThemeMode) async {
    if (newThemeMode == null) return;
    if (newThemeMode == _themeMode) return;
    _themeMode = newThemeMode;
    notifyListeners();
    await settingsLocalRepo.updateThemeMode(newThemeMode);
  }
}
