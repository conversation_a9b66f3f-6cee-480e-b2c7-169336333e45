import 'package:flutter/material.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';

import '../../../../../../../core/resources/theme/theme.dart';

class InventoryContainerWidget extends StatelessWidget {
  final Widget child;

  const InventoryContainerWidget({super.key, required this.child});

  @override
  Widget build(BuildContext context) {

    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AppSpaces.mediumPadding,
          vertical: AppSpaces.smallPadding),
      decoration: BoxDecoration(
          color: ColorManager.textFieldColor(context),
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(AppRadius.baseRadius),
            bottomRight: Radius.circular(AppRadius.baseRadius),
          )),
      child: Container(
        padding: EdgeInsets.all(AppSpaces.xSmallPadding),
        margin: EdgeInsets.only(bottom: AppSpaces.smallPadding),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.baseRadius),
            border: Border.all(
                color: context.isDark
                    ? ColorManager.grey.withOpacity(.4)
                    : ColorManager.black.withOpacity(.4))),
        child: child,
      ),
    );
  }
}

class InventoryToggleButton extends StatelessWidget {
  final bool isSelected;
  final Function() onTap;
  final String title;

  const InventoryToggleButton({
    super.key,
    required this.title,
    required this.onTap,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: InkWell(
        onTap: onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: AppSpaces.mediumPadding,
            vertical: AppSpaces.smallPadding,
          ),
          decoration: BoxDecoration(
            color: isSelected ? ColorManager.primaryColor : null,
            borderRadius: BorderRadius.circular(AppRadius.baseRadius),
          ),
          child: Center(
            child: Text(
              title,
              style: isSelected ? context.whiteLabelLarge : context.labelMedium,
            ),
          ),
        ),
      ),
    );
  }
}
