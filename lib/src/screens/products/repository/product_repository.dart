import 'dart:async';
import 'dart:io';

import 'package:idea2app_vendor_app/src/screens/dashboard/models/extra_setting_model.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/data/remote/app_exception.dart';
import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../models/products_model.dart';

class ProductRepository {
  final BaseApiServices _networkApiServices;

  ProductRepository(this._networkApiServices);

  //! Get All Products ====================================
  Future<List<ProductModel>> getAllProducts() async {
    try {
      final response =
          await _networkApiServices.getResponse(ApiEndPoints.products);

      final allProducts = <ProductModel>[];

      response.forEach((product) {
        allProducts.add(ProductModel.fromJson(product));
      });

      // final allProducts = await compute(response, responseToProductModel);

      return allProducts;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  // ! Get Product Count ====================================
  Future<bool> getProductCount() async {
    try {
      final response = await _networkApiServices
          .getResponse('${ApiEndPoints.products}?limit=1');

      if (response.isEmpty) {
        return false;
      } else {
        return true;
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  //! Get products By Category ====================================
  Future<List<ProductModel>> getProductsByCategory(
      {required String catId}) async {
    try {
      final response = await _networkApiServices.getResponse(
          '${ApiEndPoints.products}?filters[categories][documentId][\$in]=$catId&sort[0]=sort:asc',
          sortByCreatedAt: false);

      final products = <ProductModel>[];

      response.forEach((product) {
        products.add(ProductModel.fromJson(product));
      });

      return products;
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  //! Add product ============================================
  Future<void> addProduct({
    required ProductModel product,
    required List<String>? fileResult,
  }) async {
    try {
      final data = await _networkApiServices.postResponse(
        ApiEndPoints.products,
        fileResult: fileResult,
        fieldName: ApiStrings.images,
        data: product.toJson(),
      );

      // final docId = data['documentId'];

      // await _networkApiServices.connectRelation(
      //   data: {
      //     "products": {
      //       "connect": [docId]
      //     }
      //   },
      // );
      //   ApiEndPoints.vendors,
      //   data: {
      //     ApiStrings.documentId: VendorModelHelper.currentVendorDocumentId(),
      //     "products": {
      //       "connect": [docId]
      //     }
      //   },
      // );
      //
      // final vendor = VendorModel.fromJson(newVendor);
      //
      // final encodedVendor = jsonEncode(vendor.toJson(fromLocal: true));
      //
      // await GetStorageHandler.setLocalData(
      //   key: LocalKeys.vendorData,
      //   value: encodedVendor,
      // );

      return data;
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  // Future<void> addProduct(
  //     {required ProductModel product,
  //     required List<String>? fileResult}) async {
  //   try {
  //     await _networkApiServices.postResponse(ApiEndPoints.products,
  //         fileResult: fileResult,
  //         fieldName: ApiStrings.images,
  //         data: product.toJson());
  //   } on FetchDataException {
  //     rethrow;
  //   } on SocketException {
  //     rethrow;
  //   } on TimeoutException {
  //     rethrow;
  //   }
  // }

  //! Edit product ============================================
  Future<void> editProduct(
      {required ProductModel product,
      required List<String>? fileResult}) async {
    try {
      await _networkApiServices.putResponse(
        ApiEndPoints.products,
        fileResult: fileResult,
        fieldName: ApiStrings.images,
        data: product.toJson(),
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Edit Product Status ============================================
  Future<void> editProductStatus({required ProductModel product}) async {
    try {
      await _networkApiServices.putResponse(ApiEndPoints.products,
          data: product.toStatusJson());
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Update Inventory ============================================
  Future<void> updateInventory({
    required String productId,
    required int? inventory,
    required List<ExtraSettingsModel?> colors,
    required List<ExtraSettingsModel?> sizes,
  }) async {
    try {
      await _networkApiServices.putResponse(ApiEndPoints.products, data: {
        ApiStrings.documentId: productId,
        ApiStrings.inventory: inventory,
        ApiStrings.colors: colors.map((e) => e?.toProductJson()).toList(),
        ApiStrings.sizes: sizes.map((e) => e?.toProductJson()).toList(),
      });
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Delete Product Image ============================================
  Future<void> deleteProductImage(
      {required ProductModel product, required String url}) async {
    final imageId =
        product.images!.firstWhere((element) => element.url == url).id;

    // TODO Fix ----

    try {
      await _networkApiServices.deleteImage(imageId: imageId!);
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Delete product ============================================
  Future<void> deleteProduct({required String docId}) async {
    try {
      await _networkApiServices
          .deleteResponse('${ApiEndPoints.products}/$docId');
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

  //! Bulk Update Products ============================================
  Future<void> bulkUpdateProducts({
    required Map<String, Map<String, dynamic>> bulkUpdateData,
  }) async {
    try {
      await _networkApiServices.putResponse(
        'products-bulk-update',
        data: bulkUpdateData,
      );
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
