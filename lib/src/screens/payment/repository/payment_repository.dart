import 'dart:async';
import 'dart:io';

import 'package:flutter/foundation.dart';

import '../../../core/data/remote/app_exception.dart';
import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';
import '../model/payment_model.dart';

class PaymentRepository {
  final BaseApiServices networkApiServices;

  PaymentRepository({required this.networkApiServices});

  //! Get payment ====================================
  Future<List<PaymentModel>> getPayment() async {
    try {
      dynamic response =
          await networkApiServices.getResponse(ApiEndPoints.payment);

      final paymentData = await compute(responseFromPaymentModel, response);

      return paymentData;
    } catch (error) {
      rethrow;
    }
  }

  //! Add Payment ====================================
  Future<void> addPayment({required PaymentModel payment}) async {
    try {
      await networkApiServices.postResponse(
        ApiEndPoints.payment,
        data: payment.toJson(),
      );
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

//! Edit Payment ====================================
  Future<void> editPayment({
    required PaymentModel payment,
  }) async {
    try {
      await networkApiServices.putResponse(ApiEndPoints.payment,
          data: payment.toJson());
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

//! Delete Payment ====================================
  Future<void> deletePayment({required String id}) async {
    try {
      await networkApiServices.deleteResponse(
        '${ApiEndPoints.payment}/$id',
      );
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
