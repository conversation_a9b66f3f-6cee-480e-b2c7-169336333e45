import 'package:flutter/cupertino.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';

import '../../../core/consts/api_strings.dart';
import '../../../core/shared_view_models/base_view_model.dart';
import '../model/tasks_model.dart';
import '../repository/tasks_repo.dart';

class TasksVM extends BaseVM {
  final TasksRepository tasksRepository;

  TasksVM({
    required this.tasksRepository,
  });

  List<TasksModel> tasks = [];

  get total => tasks.fold(0.0, (previousValue, element) => previousValue + 0);

  get paidTotal => tasks
      .where((element) => element.isDone == true)
      .toList()
      .fold(0.0, (previousValue, element) => previousValue + 0);

  get unpaidTotal => tasks
      .where((element) => element.isDone == false)
      .toList()
      .fold(0.0, (previousValue, element) => previousValue + 0);

  Future<List<TasksModel>> getTasks(BuildContext context) async {
    return await baseFunction(context, () async {
      final tasksList = await tasksRepository.getTasks();

      tasksList.sort(
        (a, b) => (a.isDone == b.isDone ? 0 : (a.isDone ? 1 : -1)),
      );

      tasks = tasksList;

      return tasksList;
    });
  }

  Future<void> addTasks(BuildContext context,
      {required Map<String, TextEditingController> controllers,
      bool isEdit = false,
      required String filePath,
      String? documentId,
      required bool isDone}) async {
    return await baseFunction(
      context,
      () async {
        final tasks = TasksModel(
          documentId: documentId,
          isDone: isDone,
          title: controllers[ApiStrings.title]!.text,
          description: controllers[ApiStrings.description]!.text,
        );
        await tasksRepository.addEditTasks(
            isEdit: isEdit, tasks: tasks, filePath: filePath);

        await getTasks(context);
      },
      additionalFunction: (_) {
        context.back();
        if (isEdit) {
          context.showBarMessage(context.tr.updatedSuccessfully);
        } else {
          context.showBarMessage(context.tr.addedSuccessfully);
        }
      },
    );
  }

  Future<void> editTasksStatus(BuildContext context,
      {required bool isDone, required String id}) async {
    return await baseFunction(context, () async {
      final tasks = TasksModel(documentId: id, isDone: isDone);

      await tasksRepository.editTasksStatus(tasks: tasks);

      await getTasks(context);
    });
  }

  Future<void> deleteTasks(
    BuildContext context, {
    required TasksModel task,
  }) async {
    return await baseFunction(
      context,
      () async {
        context.back();
        await tasksRepository.deleteTasks(task: task);
        await getTasks(context);
      },
    );
  }
}
