import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/data/remote/app_exception.dart';
import 'package:idea2app_vendor_app/src/screens/categories/models/main_category_model.dart';
import 'package:idea2app_vendor_app/src/screens/products/repository/product_repository.dart';

import '../../../core/data/remote/network/base_api_service.dart';
import '../../../core/data/remote/response/api_end_points.dart';

class MainCategoryRepository {
  final BaseApiServices networkApiServices;
  final ProductRepository productRepository;

  MainCategoryRepository(
      {required this.networkApiServices, required this.productRepository});

  //! Get categories ====================================
  Future<List<MainCategoryModel>> getMainCategories() async {
    try {
      dynamic response = await networkApiServices.getResponse(
        '${ApiEndPoints.mainCategories}?sort[0]=sort:asc',
        sortByCreatedAt: false,
      );
      final mainCategoryData =
          (response as List).map((e) => MainCategoryModel.fromJson(e)).toList();
      return mainCategoryData;
    } catch (error) {
      rethrow;
    }
  }

//   //! add category ====================================
  Future<void> addMainCategory(
      {required MainCategoryModel cat, required String? pickedImage}) async {
    try {
      await networkApiServices.postResponse(ApiEndPoints.mainCategories,
          relationName: ApiStrings.mainCategory,
          fileResult: [pickedImage ?? ''],
          fieldName: ApiStrings.featureImage,
          // withOutVendor: true,
          connectWithVendorRelation: false,
          data: cat.toJson());

      // final docId = data['documentId'];

      // final newVendor =
      //     await networkApiServices.putResponse(ApiEndPoints.vendors, data: {
      //   ApiStrings.documentId: VendorModelHelper.currentVendorDocumentId(),
      //   "main_categories": {
      //     "connect": [docId]
      //   }
      // });
      //
      // final vendor = VendorModel.fromJson(newVendor);
      //
      // final encodedVendor = jsonEncode(vendor.toJson(
      //   fromLocal: true,
      // ));
      //
      // await GetStorageHandler.setLocalData(
      //     key: LocalKeys.vendorData, value: encodedVendor);
    } on FetchDataException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }

// //! Edit category ====================================
  Future<void> editMainCategory(
      {required MainCategoryModel cat, required String? pickedImage}) async {
    try {
      await networkApiServices.putResponse(ApiEndPoints.mainCategories,
          fileResult: [pickedImage ?? ''],
          fieldName: ApiStrings.featureImage,
          data: cat.toJson());

      if (pickedImage != null) {
        deleteCatImage(
          category: cat,
        );
      }
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

//! Delete category ====================================
  Future<void> deleteMainCategory({required MainCategoryModel category}) async {
    try {
      await Future.wait([
        //! Delete Category Images
        deleteCatImage(category: category),

        //! Delete Category
        networkApiServices.deleteResponse(
            '${ApiEndPoints.mainCategories}/${category.documentId}'),
      ]);

      // //! Delete Category Images
      // deleteCatImage(category: category);
      //
      // //! Delete Category
      // await networkApiServices.deleteResponse(
      //     '${ApiEndPoints.productsCategory}/${category.documentId}');
      //
      // //! Delete Product Category
      // deleteProductsFromCategory(category: category);
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Delete Category Image ============================================
  Future<void> deleteCatImage({required MainCategoryModel category}) async {
    try {
      final imageId = category.featureImage?.id;

      await networkApiServices.deleteImage(imageId: imageId);
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    }
  }

  //! Bulk Update Main Categories ============================================
  Future<void> bulkUpdateMainCategories({
    required Map<String, Map<String, dynamic>> mainCategoriesData,
  }) async {
    try {
      // await networkApiServices.putResponse(
      //   'main-categories-bulk-update',
      //   data: mainCategoriesData,
      // );
      await Future.forEach(mainCategoriesData.entries, (entry) async {
        final documentId = entry.key;
        final data = entry.value;

        final updatedData = {
          ApiStrings.documentId: documentId,
          ...data,
        };

        log('asfasfasfasf ${updatedData}');

        await networkApiServices.putResponse(
          ApiEndPoints.mainCategories,
          data: updatedData,
        );
      });
    } on FetchDataException {
      rethrow;
    } on TimeoutException {
      rethrow;
    } on SocketException {
      rethrow;
    }
  }
}
