import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/vendors_screen/vendors_screen.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/refresh_indictor/refresh_indictor_widget.dart';
import 'package:idea2app_vendor_app/src/screens/categories/view_model/main_category_view_model.dart';
import 'package:provider/provider.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../view_model/category_view_model.dart';
import 'widgets/categories_grid_view.dart';

class HomeCategoriesScreen extends HookWidget {
  const HomeCategoriesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final catVM = context.read<CategoryVM>();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (catVM.categories.isEmpty || currentAdminVendor != null) {
          catVM.getCategories(context);
        }
      });
      return () {};
    }, []);

    return RefreshIndicatorWidget(
      onRefresh: () {
        return catVM.getCategories(context);
      },
      child: SafeArea(
        child: Scaffold(
          appBar: currentAdminVendor != null
              ? MainAppBar(
                  haveBackButton: true,
                  title: '${currentAdminVendor?.name} ${context.tr.categories}',
                )
              : null,
          body: CategoryGridView(),
        ),
      ),
    );
  }
}
