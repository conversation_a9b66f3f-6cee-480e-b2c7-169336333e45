import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:provider/provider.dart';
import 'package:shimmer/shimmer.dart';
import '../../../../../../generated/assets.dart';
import '../../../../../core/resources/app_radius.dart';
import '../../../../../core/resources/theme/theme.dart';
import '../../../../../core/shared_widgets/dialogs/show_dialog.dart';
import '../../../../products/view/products/products_screen.dart';
import '../../../../shared/media/view_models/media_view_model.dart';
import '../../../models/category_model.dart';
import '../../../view_model/category_view_model.dart';
import '../../add_and_edit_category/widgets/edit_category_bottom_sheet_widget.dart';

class CategoryCardWidget extends StatelessWidget {
  final CategoryModel category;

  const CategoryCardWidget({super.key, required this.category});

  @override
  Widget build(BuildContext context) {
    final catVM = context.read<CategoryVM>();

    final categoryName = context.isEng
        ? (category.englishName?.isNotEmpty == true
            ? category.englishName!
            : category.arabicName ?? '')
        : (category.arabicName?.isNotEmpty == true
            ? category.arabicName!
            : category.englishName ?? '');

    return InkWell(
      onTap: () {
        context.to(ProductsScreen(category: category));
      },
      child: Stack(
        alignment: Alignment.bottomLeft,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(AppRadius.imageContainerRadius),
            child: Image.network(
              category.featureImage!.url ?? '',
              width: double.infinity,
              height: 200,
              fit: BoxFit.cover,
              errorBuilder: (_, __, ___) => SizedBox(
                  height: 200, child: Center(child: const Icon(Icons.image))),
              loadingBuilder: (context, child, loadingProgress) {
                if (loadingProgress == null) return child;
                return Center(
                  child: Shimmer(
                    gradient: LinearGradient(
                      colors: [
                        Colors.grey[300]!,
                        Colors.grey[100]!,
                      ],
                    ),
                    child: Container(
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(
                              Radius.circular(AppRadius.imageContainerRadius)),
                        )),
                  ),
                );
              },
            ),
          ),
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withAlpha(50),
                    Colors.black.withAlpha(76),
                    Colors.black.withAlpha(178),
                    Colors.black.withAlpha(230),
                    Colors.black.withAlpha(255),
                  ],
                  stops: const [
                    0.4,
                    0.7,
                    1.0,
                    1.0,
                    1.0,
                  ],
                ),
                borderRadius:
                    BorderRadius.circular(AppRadius.imageContainerRadius),
              ),
            ),
          ),
          Positioned(
              top: 0,
              right: 0,
              child: PopupMenuButton<String>(
                borderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
                splashRadius: 20,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                ),
                iconColor: ColorManager.white,
                color: context.isDark ? Color(0xff464646) : ColorManager.white,
                popUpAnimationStyle: AnimationStyle(
                  duration: const Duration(milliseconds: 400),
                  curve: Curves.easeIn,
                ),
                onSelected: (String value) {},
                itemBuilder: (BuildContext context) {
                  return [
                    PopupMenuItem(
                        onTap: () {
                          _showEditCategoryBottomSheet(context, category);
                        },
                        child: Row(
                          children: [
                            if (context.isDark) ...[
                              BaseLottieWidget.icon(
                                Assets.animatedEdit,
                                height: 20.h,
                              ),
                              context.smallGap,
                            ],
                            Text(context.tr.editCategory,
                                style: context.labelMedium)
                          ],
                        )),
                    PopupMenuDivider(
                      color: context.appTheme.primaryColorDark.withAlpha(50),
                    ),
                    PopupMenuItem(
                        onTap: () => showAlertDialog(
                              context,
                              child: Text(
                                  context.tr.deleteCategoryConfirmationMessage,
                                  style: context.labelLarge),
                              isLoading: catVM.isLoading,
                              onConfirm: () async {
                                await catVM.deleteCategory(context,
                                    category: category);
                                catVM.getSearchedCategories('');
                              },
                            ),
                        child: Row(
                          children: [
                            if (context.isDark) ...[
                              BaseLottieWidget.icon(
                                Assets.animatedDelete,
                                height: 15.h,
                              ),
                              context.smallGap,
                            ],
                            Text(context.tr.deleteCategory,
                                style: context.labelMedium)
                          ],
                        )),
                  ];
                },
              )),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    categoryName,
                    textAlign: context.isEng ? TextAlign.left : TextAlign.right,
                    style: context.whiteLabelLarge.copyWith(
                        fontWeight: context.isEng ? null : FontWeight.bold),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                CircleAvatar(
                  radius: 15.r,
                  backgroundColor: ColorManager.darkGrey.withAlpha(51),
                  child: Transform.rotate(
                    angle: -0.6978,
                    child: Icon(Icons.arrow_forward,
                        size: 16.r, color: ColorManager.white),
                  ),
                ).frosted(
                  blur: 0.9,
                  frostColor: Colors.grey.withAlpha(25),
                  borderRadius: BorderRadius.circular(20),
                ),
              ],
            ).paddingAll(AppSpaces.smallPadding),
          ),
        ],
      ),
    );
  }

  void _showEditCategoryBottomSheet(
      BuildContext context, CategoryModel category) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EditCategoryBottomSheet(category: category),
    );
  }
}
