import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:provider/provider.dart';
import '../../../../../core/resources/app_radius.dart';
import '../../../../shared/media/view_models/media_view_model.dart';
import '../../../models/category_model.dart';
import '../../../view_model/category_view_model.dart';
import 'add_category_fields.dart';

class EditCategoryBottomSheet extends HookWidget {
  final CategoryModel category;

  const EditCategoryBottomSheet({
    super.key,
    required this.category,
  });

  @override
  Widget build(BuildContext context) {
    final englishNameController =
        useTextEditingController(text: category.englishName);
    final arabicNameController =
        useTextEditingController(text: category.arabicName);

    final categoryVM = context.read<CategoryVM>();
    final mediaVM = context.read<MediaVM>();
    final formKey = useState(GlobalKey<FormState>());

    void editCategory() async {
      if (englishNameController.text.isEmpty &&
          arabicNameController.text.isEmpty) {
        context.showBarMessage(context.tr.youMustAddOneOfThoseNames,
            isError: true);
        return;
      }

      final cat = category.copyWith(
          documentId: category.documentId,
          name: englishNameController.text,
          arabicName: arabicNameController.text,
          mainCategory: category.mainCategory,
          sortNumber: category.sortNumber);

      await categoryVM.editCategory(context,
          cat: cat, pickedImage: mediaVM.filesPaths.firstOrNull);

      mediaVM.clearFiles();
    }

    void validateAndEditCategory() async {
      if (!formKey.value.currentState!.validate()) {
        categoryVM.resetErrorWithDuration();
        return;
      }

      editCategory();
    }

    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: BoxDecoration(
        color: context.appTheme.scaffoldBackgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(AppRadius.baseContainerRadius),
          topRight: Radius.circular(AppRadius.baseContainerRadius),
        ),
      ),
      child: Form(
        key: formKey.value,
        child: Consumer<CategoryVM>(
          builder: (context, categoryVM, child) {
            return SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    width: 40,
                    height: 5,
                    margin: EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      color: Colors.grey[400],
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),

                  // Title
                  Text(
                    context.tr.editCategory,
                    style: context.title,
                  ).paddingOnly(bottom: AppSpaces.mediumPadding),

                  AddCategoryWidgets(
                    englishNameController: englishNameController,
                    arabicNameController: arabicNameController,
                    category: category,
                  ),

                  context.xLargeGap,

                  // Edit Category Button
                  Button(
                          isLoading: categoryVM.isLoading,
                          label: context.tr.edit,
                          onPressed: validateAndEditCategory)
                      .paddingOnly(bottom: AppSpaces.largePadding),
                ],
              ).paddingAll(AppSpaces.mediumPadding),
            );
          },
        ),
      ),
    );
  }
}
