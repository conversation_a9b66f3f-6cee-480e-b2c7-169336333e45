import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view/add_vendor/widgets/pricing_drop_down.dart';
import 'package:idea2app_vendor_app/src/admin/vendors/view_model/vendors_view_model.dart';
import 'package:idea2app_vendor_app/src/core/consts/api_strings.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_radius.dart';
import 'package:idea2app_vendor_app/src/core/resources/app_spaces.dart';
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/appbar/main_appbar.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/fields/date_picker.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/shared_widgets.dart';
import 'package:idea2app_vendor_app/src/core/shared_widgets/switch_button_widget/switch_button_widget.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/helper_models/pricing_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/models/vendor_model.dart';
import 'package:idea2app_vendor_app/src/screens/auth/view/register/widgets/templates_drop_down.dart';
import 'package:idea2app_vendor_app/src/screens/settings/model/settings_model.dart';
import 'package:idea2app_vendor_app/src/screens/settings/view/contact_us.dart';
import 'package:idea2app_vendor_app/src/screens/shared/media/view_models/media_view_model.dart';
import 'package:provider/provider.dart';

class AddVendorFloatingButton extends StatelessWidget {
  const AddVendorFloatingButton({
    super.key,
  });

  @override
  Widget build(
    BuildContext context,
  ) {
    return FloatingActionButton(
      backgroundColor: ColorManager.primaryColor,
      onPressed: () => context.to(const AddEditVendorScreen()),
      child: const Icon(Icons.add, color: Colors.white),
    );
  }
}

class AddEditVendorScreen extends HookWidget {
  final VendorModel? vendor;

  const AddEditVendorScreen({
    super.key,
    this.vendor,
  });

  @override
  Widget build(BuildContext context) {
    final vendorsVM = context.read<VendorsVM>();
    final watchVendorVM = context.watch<VendorsVM>();

    final isEdit = vendor != null;

    final mediaVM = context.watch<MediaVM>();

    final websiteLink = vendor?.websiteLink ??
        'https://i2shop.store/${vendor?.businessName ?? ''}';

    final controllers = {
      ApiStrings.name: useTextEditingController(text: vendor?.name),
      ApiStrings.phone: useTextEditingController(text: vendor?.phone),
      ApiStrings.businessName:
          useTextEditingController(text: vendor?.businessName),
      ApiStrings.websiteLink: useTextEditingController(text: websiteLink),
      ApiStrings.email: useTextEditingController(text: vendor?.email),
      ApiStrings.address: useTextEditingController(text: vendor?.address),
      ApiStrings.password: useTextEditingController(text: vendor?.password),
      ApiStrings.startDate:
          useTextEditingController(text: vendor?.startDate.toString()),
      ApiStrings.expireDate:
          useTextEditingController(text: vendor?.expireDate.toString()),
      ApiStrings.paidAmount:
          useTextEditingController(text: vendor?.paidAmount.toString()),
    };

    final valueNotifiers = {
      ApiStrings.pricing: useState<PricingModel?>(vendor?.pricing),
      ApiStrings.vendorType: useState(vendor?.vendorType ?? VendorType.free),
      ApiStrings.businessType: useState<TemplateModel?>(isEdit
          ? TemplateModel(
              name: vendor?.businessType ?? '',
            )
          : null),
      ApiStrings.isActive: useState(vendor?.isActive ?? true),
      ApiStrings.startDate:
          useState(isEdit ? vendor?.startDate : DateTime.now()),
      ApiStrings.expireDate: useState(vendor?.expireDate)
    };

    final formKey = useState(GlobalKey<FormState>());

    final title = isEdit ? context.tr.editVendor : context.tr.addNewVendor;

    void validateAndAddOrEditVendor() async {
      if (formKey.value.currentState!.validate()) {
        if (isEdit) {
          await vendorsVM.editVendor(
            context,
            controllers: controllers,
            valueNotifiers: valueNotifiers,
            pickedImage: mediaVM.filesPaths.firstOrNull ?? '',
            vendor: vendor,
          );
        } else {
          await vendorsVM.addVendor(
            context,
            controllers: controllers,
            valueNotifiers: valueNotifiers,
            pickedImage: mediaVM.filesPaths.firstOrNull ?? '',
          );
        }
      }
    }

    return Scaffold(
      appBar: MainAppBar(
        onBackPressed: () {
          mediaVM.clearFiles();
          context.back();
        },
        title: title,
        haveBackButton: true,
      ),
      body: Form(
        key: formKey.value,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppSpaces.largePadding - 4),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SinglePickImageWidget(
                networkImage: vendor?.logo?.url,
                pickedResult: mediaVM.filesPaths.firstOrNull,
              ),

              context.largeGap,

              //! Business Name Field
              Row(
                children: [
                  //! Name Field
                  Expanded(
                    child: BaseTextField(
                      controller: controllers[ApiStrings.name],
                      title: context.tr.name,
                      textInputType: TextInputType.name,
                      hint: context.tr.name,
                    ),
                  ),

                  context.smallGap,

                  //! Business Name Field
                  Expanded(
                    child: BaseTextField(
                      controller: controllers[ApiStrings.businessName],
                      title: context.tr.businessName,
                      onChanged: (value) {
                        if (isEdit) return;
                        controllers[ApiStrings.websiteLink]?.text =
                            'https://i2shop.store/$value';
                      },
                      textInputType: TextInputType.name,
                    ),
                  ),
                ],
              ),

              context.largeGap,

              //! Business Type
              Row(
                children: [
                  Expanded(
                      child: TemplatesDropDown(
                          showTemplateLink: false,
                          selectedBusinessType:
                              valueNotifiers[ApiStrings.businessType]!
                                  as ValueNotifier<TemplateModel?>)),

                  context.smallGap,

                  //! Vendor Type
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr.subscriptionType,
                          style: context.labelLarge,
                          textAlign: TextAlign.start,
                        ),
                        context.smallGap,
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(
                            left: kIsWeb ? 0.0 : 12,
                            right: kIsWeb ? 0.0 : 12,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(
                                AppRadius.extraLargeContainerRadius),
                            color: context.isDark
                                ? Colors.grey.withOpacity(.1)
                                : Colors.grey.withOpacity(0.15),
                          ),
                          child: BaseDropDown<VendorType>(
                            isExpanded: true,
                            value: valueNotifiers[ApiStrings.vendorType]!.value,
                            data: VendorType.values
                                .map((e) => DropdownMenuItem(
                                      value: e,
                                      child: Text(e.toString().split('.').last),
                                    ))
                                .toList(),
                            onChanged: (value) {
                              valueNotifiers[ApiStrings.vendorType]!.value =
                                  value!;

                              final vendorExpireDate =
                                  vendor?.expireDate ?? DateTime.now();

                              final expireDate =
                                  valueNotifiers[ApiStrings.expireDate]!;

                              if (value == VendorType.monthly) {
                                expireDate.value = vendorExpireDate
                                    .add(const Duration(days: 31));
                              } else if (value == VendorType.quarter) {
                                expireDate.value = vendorExpireDate
                                    .add(const Duration(days: 92));
                              } else if (value == VendorType.semester) {
                                expireDate.value = vendorExpireDate
                                    .add(const Duration(days: 184));
                              } else if (value == VendorType.annually) {
                                expireDate.value = vendorExpireDate
                                    .add(const Duration(days: 365));
                              } else {
                                expireDate.value = null;
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              context.largeGap,

              Row(
                children: [
                  Expanded(
                    child: BaseDatePicker(
                      title: context.tr.startDate,
                      selectedDateNotifier:
                          valueNotifiers[ApiStrings.startDate]!
                              as ValueNotifier<DateTime?>,
                      label: context.tr.startDate,
                    ),
                  ),
                  context.smallGap,
                  Expanded(
                    child: BaseDatePicker(
                      title: context.tr.expireDate,
                      selectedDateNotifier:
                          valueNotifiers[ApiStrings.expireDate]!
                              as ValueNotifier<DateTime?>,
                      label: context.tr.expireDate,
                    ),
                  ),
                ],
              ),

              context.largeGap,

              //! Paid Amount Field
              Row(
                children: [
                  Expanded(
                      flex: 2,
                      child: PricingDropDown(
                        selectedPricing: valueNotifiers[ApiStrings.pricing]!
                            as ValueNotifier<PricingModel?>,
                        vendorType: valueNotifiers[ApiStrings.vendorType]!.value
                            as VendorType,
                        paidAmountController:
                            controllers[ApiStrings.paidAmount]!,
                      )),
                  context.smallGap,
                  Expanded(
                    child: BaseTextField(
                      title: context.tr.paidAmount,
                      hint: context.tr.paidAmount,
                      isRequired: false,
                      controller: controllers[ApiStrings.paidAmount],
                    ),
                  ),
                ],
              ),

              context.largeGap,

              //! Website Name Field
              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: BaseTextField(
                      controller: controllers[ApiStrings.websiteLink],
                      title: context.tr.websiteName,
                      textInputType: TextInputType.name,
                    ),
                  ),
                  context.smallGap,
                  IconButton(
                    onPressed: () {
                      openURL(websiteLink);
                    },
                    icon: const CircleAvatar(
                        backgroundColor: ColorManager.primaryColor,
                        child: Icon(
                          Icons.open_in_new,
                          color: Colors.white,
                        )),
                  ),
                ],
              ),

              context.largeGap,

              //! Email Field
              BaseTextField(
                controller: controllers[ApiStrings.email],
                title: context.tr.email,
                hint: context.tr.email,
                textInputType: TextInputType.emailAddress,
              ),

              context.largeGap,

              //! Password Field
              BaseTextField(
                controller: controllers[ApiStrings.password],
                title: context.tr.password,
                textInputType: TextInputType.visiblePassword,
                hint: context.tr.password,
              ),

              context.largeGap,

              //! Phone Field
              BaseTextField(
                controller: controllers[ApiStrings.phone],
                title: context.tr.phone,
                hint: context.tr.phone,
                textInputType: TextInputType.phone,
                isRequired: false,
              ),

              context.largeGap,

              //! Address Field
              BaseTextField(
                radius: AppRadius.longFieldRadius,
                controller: controllers[ApiStrings.address],
                title: context.tr.address,
                hint: context.tr.address,
                textInputType: TextInputType.streetAddress,
                maxLines: 4,
                isRequired: false,
              ),

              context.largeGap,

              Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Expanded(
                    child: Button(
                      isLoading: watchVendorVM.isLoading,
                      label: isEdit ? context.tr.edit : context.tr.submit,
                      onPressed: validateAndAddOrEditVendor,
                    ),
                  ),
                  context.largeGap,
                  SwitchButtonWidget(
                    title: context.tr.isActive,
                    onChanged: (value) {
                      valueNotifiers[ApiStrings.isActive]!.value = value;
                    },
                    value: valueNotifiers[ApiStrings.isActive]
                        as ValueNotifier<bool>,
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}
