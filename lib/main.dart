import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_gemini/flutter_gemini.dart';
import 'package:flutter_phoenix/flutter_phoenix.dart';
import 'package:get_storage/get_storage.dart';
import 'package:idea2app_vendor_app/firebase_options.dart';
import 'package:idea2app_vendor_app/src/core/consts/app_constants.dart';
import 'package:idea2app_vendor_app/src/core/extensions/extensions.dart';
import 'package:idea2app_vendor_app/src/core/injector/injector.dart'
    as injector;
import 'package:idea2app_vendor_app/src/core/resources/theme/theme.dart';
import 'package:idea2app_vendor_app/src/core/services/notifications_service.dart';
import 'package:idea2app_vendor_app/src/screens/shared/base_multi_provider/base_multi_provider.dart';
import 'package:idea2app_vendor_app/src/screens/splash_screen/view/splash_screen.dart';
import 'package:path_provider/path_provider.dart';
import 'package:universal_platform/universal_platform.dart';
import 'package:url_strategy/url_strategy.dart';

//! flutter build web --release --web-renderer canvaskit

//! flutter build web --release --web-renderer html (to view the images)
//! firebase deploy --only hosting

Future main() async {
  WidgetsFlutterBinding.ensureInitialized();

  Gemini.init(apiKey: AppConsts.geminiApiKey, enableDebugging: kDebugMode);

  if (kIsWeb) {
    setPathUrlStrategy();
  }

  await GetStorage.init();

  String storageLocation =
      kIsWeb ? '' : (await getApplicationDocumentsDirectory()).path;

  // await Future.wait([
  //   FastCachedImageConfig.init(
  //     subDir: storageLocation,
  //   ),
  //   injector.appInjector(),
  //   FastCachedImageConfig.init(),
  //   if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple))
  //     Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform),
  // ]);
  //
  // if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple)) {
  //   NotificationService.init();
  // }

  if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple)) {
    await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform);
  }

  await Future.wait([
    FastCachedImageConfig.init(subDir: storageLocation),
    injector.appInjector(),
    FastCachedImageConfig.init(),
  ]);

  if (kReleaseMode || (kDebugMode && !UniversalPlatform.isApple)) {
    NotificationService.init();
  }
  runApp(Phoenix(child: const BaseMultiProvider(child: BuildAppView())));
}

class BuildAppView extends StatelessWidget {
  const BuildAppView({super.key});

  @override
  Widget build(BuildContext context) {
    final isDarkTheme = context.isDark;

    Widget buildRunnableApp({
      required double webAppWidth,
      required Widget app,
    }) {
      if (!kIsWeb) {
        return app;
      }

      return Directionality(
        textDirection: TextDirection.ltr,
        child: Stack(
          children: [
            Container(
              color: isDarkTheme
                  ? ColorManager.darkBackgroundColor
                  : ColorManager.backgroundColor,
              width: double.infinity,
              height: double.infinity,
            ),
            Center(
              child: ClipRect(
                child: SizedBox(
                  width: webAppWidth,
                  child: app,
                ),
              ),
            ),
          ],
        ),
      );
    }

    final runnableApp = buildRunnableApp(
      webAppWidth: 800.0,
      app: const SplashScreen(),
    );

    return runnableApp;
  }
}
